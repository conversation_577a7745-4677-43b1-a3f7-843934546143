import 'dart:convert';
import 'dart:io';

// Simple HMAC-SHA256 implementation for signature calculation
import 'package:crypto/crypto.dart';

void main() {
  const String secretKey = "superSecretKey123";
  const String phoneNumber = "09123456789";
  const String message = "سلام! این یک پیام تست است.";
  const String timestamp = "1692800000";
  
  // Create the data string to sign: phoneNumber + message + timestamp
  final dataToSign = phoneNumber + message + timestamp;
  
  // Calculate HMAC-SHA256
  final key = utf8.encode(secretKey);
  final bytes = utf8.encode(dataToSign);
  final hmacSha256 = Hmac(sha256, key);
  final digest = hmacSha256.convert(bytes);
  
  // Convert to hex string
  final signature = digest.toString();
  
  print('=== SIGNATURE CALCULATION ===');
  print('Secret Key: $secretKey');
  print('Phone Number: $phoneNumber');
  print('Message: $message');
  print('Timestamp: $timestamp');
  print('Data to Sign: $dataToSign');
  print('Calculated Signature: $signature');
  print('');
  
  print('=== VALID FCM PAYLOAD ===');
  final payload = {
    'type': 'special_sms',
    'phoneNumber': phoneNumber,
    'message': message,
    'timestamp': timestamp,
    'signature': signature,
  };
  
  print(jsonEncode(payload));
  print('');
  
  print('=== COMPLETE FCM MESSAGE ===');
  final fcmMessage = {
    'to': 'FIREBASE_TOKEN_HERE',
    'data': payload,
    'notification': {
      'title': 'SMS Notification',
      'body': 'New SMS to be sent to $phoneNumber',
    },
  };
  
  print(jsonEncode(fcmMessage));
}
